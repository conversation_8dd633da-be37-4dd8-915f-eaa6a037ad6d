"""
UI绘制模块测试 - 验证ui_drawing.py功能
测试从ui_handler.py拆分出的绘制相关函数
"""

import sys
import os
import unittest
import cv2
import numpy as np
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_base import BaseTestCase
import ui_drawing
from app_state import AppState
from constants import *


class TestUIDrawing(BaseTestCase):
    """UI绘制功能测试"""
    
    def setUp(self):
        super().setUp()
        # 设置测试用的ROI数据
        self.setup_test_rois()
    
    def setup_test_rois(self):
        """设置测试用的ROI数据"""
        # LED ROI测试数据
        self.app_state.led_rois = [
            (100, 100, 50, 50),  # G1
            (200, 100, 50, 50),  # G2
            (300, 100, 50, 50),  # R1
            None,  # 未定义的ROI
        ]
        self.app_state.led_num_green = 2
        self.app_state.led_num_red = 2
        self.app_state.led_max_rois = 4
        
        # 数码管ROI测试数据
        self.app_state.digit_rois = [
            (100, 300, 80, 120),  # D1
            (200, 300, 80, 120),  # D2
        ]
        
        # 数码管段ROI测试数据
        self.app_state.digit_segment_rois = [
            [(110, 310, 60, 10), (170, 320, 10, 40), (170, 370, 10, 40), 
             (110, 400, 60, 10), (100, 370, 10, 40), (100, 320, 10, 40), (110, 355, 60, 10)],  # D1的7段
            [(210, 310, 60, 10), (270, 320, 10, 40), (270, 370, 10, 40), 
             (210, 400, 60, 10), (200, 370, 10, 40), (200, 320, 10, 40), (210, 355, 60, 10)],  # D2的7段
        ]
        
        # 基准点测试数据
        self.app_state.base_points = [(50, 50), (550, 50)]
        self.app_state.base_template_size = 30
    
    def test_draw_rois_function_exists(self):
        """测试draw_rois函数是否存在且可调用"""
        self.assert_function_callable(ui_drawing, 'draw_rois')
    
    def test_draw_hud_function_exists(self):
        """测试draw_hud函数是否存在且可调用"""
        self.assert_function_callable(ui_drawing, 'draw_hud')
    
    def test_draw_rois_calibration_mode(self):
        """测试校准模式下的ROI绘制"""
        # 设置校准模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        self.app_state.calib_led_roi_index = 2
        
        # 执行绘制
        original_frame = self.app_state.display_frame.copy()
        ui_drawing.draw_rois(self.app_state)
        
        # 验证帧被修改（绘制了内容）
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame),
                        "校准模式下应该绘制ROI内容")
    
    def test_draw_rois_detection_mode(self):
        """测试检测模式下的ROI绘制"""
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 设置LED状态
        self.app_state.led_last_status = [True, False, True, False]
        self.app_state.led_last_values = [(200, 180, 100), (100, 90, 50), (150, 80, 200), (80, 70, 60)]
        
        # 设置数码管状态
        self.app_state.digit_last_recognized_chars = ['8', '5']
        self.app_state.digit_last_segment_patterns = [
            [1, 1, 1, 1, 1, 1, 1],  # 8的段模式
            [1, 0, 1, 1, 0, 1, 1],  # 5的段模式
        ]
        self.app_state.digit_last_missing_segments = [[], []]
        
        # 执行绘制
        original_frame = self.app_state.display_frame.copy()
        ui_drawing.draw_rois(self.app_state)
        
        # 验证帧被修改
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame),
                        "检测模式下应该绘制状态信息")
    
    def test_draw_rois_base_points(self):
        """测试基准点绘制"""
        # 设置基准点选择模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
        
        # 执行绘制
        original_frame = self.app_state.display_frame.copy()
        ui_drawing.draw_rois(self.app_state)
        
        # 验证帧被修改
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame),
                        "基准点模式下应该绘制基准点")
    
    def test_draw_rois_with_none_frame(self):
        """测试display_frame为None时的处理"""
        self.app_state.display_frame = None
        
        # 应该不抛出异常
        try:
            ui_drawing.draw_rois(self.app_state)
        except Exception as e:
            self.fail(f"draw_rois在display_frame为None时不应抛出异常: {e}")
    
    def test_draw_hud_detection_mode(self):
        """测试检测模式下的HUD绘制"""
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 设置FPS和其他状态
        self.app_state.fps = 30.5
        self.app_state.window_topmost = True
        self.app_state.digit_brightness_threshold = 50.0
        
        # 执行绘制
        original_frame = self.app_state.display_frame.copy()
        ui_drawing.draw_hud(self.app_state)
        
        # 验证帧被修改
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame),
                        "HUD绘制应该修改显示帧")
    
    def test_draw_hud_with_none_frame(self):
        """测试display_frame为None时的HUD处理"""
        self.app_state.display_frame = None
        
        # 应该不抛出异常
        try:
            ui_drawing.draw_hud(self.app_state)
        except Exception as e:
            self.fail(f"draw_hud在display_frame为None时不应抛出异常: {e}")
    
    def test_performance_draw_rois(self):
        """测试draw_rois性能"""
        self.app_state.current_mode = MODE_DETECTION
        
        # 测量性能
        result, execution_time = self.measure_performance(ui_drawing.draw_rois, self.app_state)
        
        # 验证性能（应该在10ms内完成）
        self.assertLess(execution_time, 0.01, 
                       f"draw_rois执行时间过长: {execution_time:.4f}s")
    
    def test_performance_draw_hud(self):
        """测试draw_hud性能"""
        self.app_state.current_mode = MODE_DETECTION
        
        # 测量性能
        result, execution_time = self.measure_performance(ui_drawing.draw_hud, self.app_state)
        
        # 验证性能（应该在20ms内完成）
        self.assertLess(execution_time, 0.02, 
                       f"draw_hud执行时间过长: {execution_time:.4f}s")
    
    def test_roi_drawing_consistency(self):
        """测试ROI绘制的一致性"""
        # 多次绘制相同状态，结果应该一致
        self.app_state.current_mode = MODE_DETECTION
        
        # 第一次绘制
        frame1 = self.app_state.display_frame.copy()
        ui_drawing.draw_rois(self.app_state)
        result1 = self.app_state.display_frame.copy()
        
        # 重置帧并第二次绘制
        self.app_state.display_frame = frame1.copy()
        ui_drawing.draw_rois(self.app_state)
        result2 = self.app_state.display_frame.copy()
        
        # 结果应该一致
        self.assertTrue(np.array_equal(result1, result2),
                       "相同状态下的ROI绘制结果应该一致")


if __name__ == '__main__':
    unittest.main()
