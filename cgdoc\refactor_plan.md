# UI Handler 重构计划

## 📋 重构目标
- **问题**：ui_handler.py 文件过大（近2000行），职责过重
- **目标**：拆分为4个模块，保持功能100%不变
- **原则**：🔒功能不变 🔄随时回滚 📊可验证

## 📁 文件结构规划

### 重构前
```
ui_handler.py (1956行) - 所有功能集中
```

### 重构后
```
ui_handler.py (新版本，导入组装) - 主协调器
├── ui_drawing.py     # 绘制相关函数 (~500行)
├── ui_modes.py       # 模式处理函数 (~600行)
├── ui_events.py      # 事件处理函数 (~400行)
└── ui_handler_original.py (备份原版)
```

## 🎯 模块职责划分

### ui_drawing.py
- `draw_rois()` - ROI绘制逻辑
- `draw_hud()` - HUD信息显示
- 相关绘制辅助函数

### ui_modes.py
- `_run_camera_settings_mode()` - 摄像头设置模式
- `_run_calibration_mode()` - 校准模式处理
- `_run_detection_mode()` - 检测模式处理

### ui_events.py
- `_mouse_callback()` - 鼠标事件处理
- `setup_mouse_callback()` - 鼠标回调设置
- `get_key()` - 按键获取
- `toggle_window_topmost()` - 窗口置顶

### ui_handler.py (重构后)
- `process_core_logic()` - 核心处理逻辑
- `prepare_display_frame()` - 显示帧准备
- 导入并协调其他模块

## 📊 重构阶段

### 阶段1：准备工作 ✅
- [x] 创建cgtestscripts和cgdoc目录
- [x] 编写重构计划文档
- [ ] 备份原始ui_handler.py
- [ ] 创建基础测试框架
- [ ] 记录功能基线

### 阶段2：模块拆分
- [ ] 提取ui_drawing.py
- [ ] 提取ui_modes.py  
- [ ] 提取ui_events.py
- [ ] 每个模块拆分后立即测试

### 阶段3：集成验证
- [ ] 更新ui_handler.py使用新模块
- [ ] 运行完整测试套件
- [ ] 性能对比验证
- [ ] 功能一致性验证

### 阶段4：文档和清理
- [ ] 完善文档记录
- [ ] 清理临时文件
- [ ] 提供回滚指南

## ⚠️ 安全保障

1. **原子性**：每次只修改一个模块
2. **即时验证**：每步修改后立即测试
3. **回滚机制**：保持原始文件完整
4. **核心保护**：绝不修改核心算法模块

## 📈 成功标准

- ✅ 所有现有功能正常工作
- ✅ 性能没有明显下降  
- ✅ 代码结构更清晰易维护
- ✅ 测试覆盖率充分
- ✅ 文档完整可追溯

## 🔄 回滚方案

如需回滚到原始状态：
1. 删除新创建的模块文件
2. 将ui_handler_original.py重命名为ui_handler.py
3. 验证系统正常运行
