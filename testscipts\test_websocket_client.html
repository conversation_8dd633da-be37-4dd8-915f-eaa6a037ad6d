<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket客户端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .countdown {
            font-size: 24px;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border-radius: 8px;
        }
        .progress-circle {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }
        .progress-circle svg {
            transform: rotate(-90deg);
        }
        .progress-circle .progress {
            transition: stroke-dasharray 1s ease-linear;
        }
        .message-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视觉检测WebSocket客户端测试</h1>
        
        <!-- 连接状态 -->
        <div id="connectionStatus" class="status disconnected">
            未连接到WebSocket服务器
        </div>
        
        <!-- 控制按钮 -->
        <div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button id="queryBtn" onclick="queryStatus()" disabled>查询状态</button>
            <button id="clearBtn" onclick="clearLog()">清空日志</button>
        </div>
        
        <!-- 倒计时显示 -->
        <div id="countdownContainer" style="display: none;">
            <div class="countdown">
                <div class="progress-circle">
                    <svg width="100" height="100" viewBox="0 0 36 36">
                        <path
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            fill="none"
                            stroke="#e5e7eb"
                            stroke-width="2"
                        />
                        <path
                            id="progressPath"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            fill="none"
                            stroke="#ffffff"
                            stroke-width="2"
                            stroke-dasharray="0, 100"
                            class="progress"
                        />
                    </svg>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; font-weight: bold;">
                        <span id="countdownNumber">0</span>
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <div id="statusMessage">等待检测...</div>
                    <div id="startTime" style="font-size: 14px; opacity: 0.8;"></div>
                </div>
            </div>
        </div>
        
        <!-- 消息日志 -->
        <h3>消息日志</h3>
        <div id="messageLog" class="message-log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        function log(message) {
            const logElement = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const queryBtn = document.getElementById('queryBtn');
            
            isConnected = connected;
            
            if (connected) {
                statusElement.textContent = '已连接到WebSocket服务器';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                queryBtn.disabled = false;
            } else {
                statusElement.textContent = '未连接到WebSocket服务器';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                queryBtn.disabled = true;
            }
        }
        
        function updateCountdown(data) {
            const container = document.getElementById('countdownContainer');
            const countdownNumber = document.getElementById('countdownNumber');
            const statusMessage = document.getElementById('statusMessage');
            const startTime = document.getElementById('startTime');
            const progressPath = document.getElementById('progressPath');
            
            if (data.is_active) {
                container.style.display = 'block';
                countdownNumber.textContent = data.countdown;
                statusMessage.textContent = data.message;
                startTime.textContent = data.start_time ? `开始时间: ${data.start_time}` : '';
                
                // 更新进度圆圈
                const progress = data.progress || 0;
                progressPath.setAttribute('stroke-dasharray', `${progress}, 100`);
            } else {
                if (data.state === 'COMPLETED') {
                    // 显示完成状态3秒后隐藏
                    statusMessage.textContent = data.message;
                    setTimeout(() => {
                        container.style.display = 'none';
                    }, 3000);
                } else {
                    container.style.display = 'none';
                }
            }
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接');
                return;
            }
            
            log('正在连接WebSocket服务器...');
            ws = new WebSocket('ws://localhost:8766');
            
            ws.onopen = function(event) {
                log('WebSocket连接成功');
                updateConnectionStatus(true);
                queryStatus();
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.type === 'status_update') {
                        updateCountdown(data.data);
                    }
                } catch (e) {
                    log(`解析消息失败: ${e.message}`);
                }
            };
            
            ws.onclose = function(event) {
                log(`WebSocket连接关闭: ${event.code} ${event.reason}`);
                updateConnectionStatus(false);
            };
            
            ws.onerror = function(error) {
                log(`WebSocket错误: ${error}`);
                updateConnectionStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateConnectionStatus(false);
            log('WebSocket连接已断开');
        }
        
        function queryStatus() {
            if (!isConnected) {
                log('WebSocket未连接，无法查询状态');
                return;
            }
            
            const message = {
                type: 'query_status',
                data: {
                    timestamp: new Date().toISOString()
                }
            };
            
            ws.send(JSON.stringify(message));
            log('已发送状态查询请求');
        }
        
        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            log('页面加载完成，准备连接WebSocket服务器');
            connect();
        };
        
        // 页面卸载时断开连接
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html>
