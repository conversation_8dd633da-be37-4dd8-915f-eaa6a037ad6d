"""
基础测试框架 - UI Handler重构测试
提供通用的测试工具和验证函数
"""

import sys
import os
import unittest
import time
import cv2
import numpy as np
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_state import AppState
from constants import *


class BaseTestCase(unittest.TestCase):
    """基础测试类，提供通用的测试工具"""
    
    def setUp(self):
        """测试前准备"""
        self.app_state = AppState()
        self.setup_mock_camera()
        self.setup_test_frame()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self.app_state, 'cap') and self.app_state.cap:
            self.app_state.cap.release()
    
    def setup_mock_camera(self):
        """设置模拟摄像头"""
        self.app_state.cap = Mock()
        self.app_state.cap.isOpened.return_value = True
        self.app_state.cap.read.return_value = (True, self.create_test_frame())
    
    def setup_test_frame(self):
        """设置测试帧"""
        self.test_frame = self.create_test_frame()
        self.app_state.current_frame = self.test_frame
        self.app_state.display_frame = self.test_frame.copy()
    
    def create_test_frame(self, width=640, height=480):
        """创建测试用的图像帧"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        # 添加一些测试图案
        cv2.rectangle(frame, (100, 100), (200, 200), (0, 255, 0), -1)  # 绿色矩形
        cv2.rectangle(frame, (300, 300), (400, 400), (0, 0, 255), -1)  # 红色矩形
        return frame
    
    def assert_function_exists(self, module, function_name):
        """验证函数是否存在"""
        self.assertTrue(hasattr(module, function_name), 
                       f"函数 {function_name} 不存在于模块中")
    
    def assert_function_callable(self, module, function_name):
        """验证函数是否可调用"""
        self.assert_function_exists(module, function_name)
        func = getattr(module, function_name)
        self.assertTrue(callable(func), 
                       f"函数 {function_name} 不可调用")
    
    def assert_frame_valid(self, frame):
        """验证图像帧是否有效"""
        self.assertIsNotNone(frame, "图像帧不能为None")
        self.assertIsInstance(frame, np.ndarray, "图像帧必须是numpy数组")
        self.assertEqual(len(frame.shape), 3, "图像帧必须是3维数组")
        self.assertEqual(frame.shape[2], 3, "图像帧必须是3通道")
    
    def assert_roi_valid(self, roi):
        """验证ROI是否有效"""
        if roi is not None:
            self.assertIsInstance(roi, (tuple, list), "ROI必须是元组或列表")
            self.assertEqual(len(roi), 4, "ROI必须包含4个元素(x,y,w,h)")
            x, y, w, h = roi
            self.assertGreaterEqual(x, 0, "ROI x坐标必须非负")
            self.assertGreaterEqual(y, 0, "ROI y坐标必须非负")
            self.assertGreater(w, 0, "ROI宽度必须大于0")
            self.assertGreater(h, 0, "ROI高度必须大于0")
    
    def measure_performance(self, func, *args, **kwargs):
        """测量函数执行性能"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
    
    def compare_frames(self, frame1, frame2, tolerance=0):
        """比较两个图像帧是否相同"""
        if frame1 is None and frame2 is None:
            return True
        if frame1 is None or frame2 is None:
            return False
        if frame1.shape != frame2.shape:
            return False
        
        diff = cv2.absdiff(frame1, frame2)
        return np.max(diff) <= tolerance


class FunctionSignatureTest(BaseTestCase):
    """函数签名一致性测试"""
    
    def test_function_signature_consistency(self):
        """测试重构前后函数签名一致性"""
        # 这个测试将在重构过程中使用
        pass


class PerformanceTest(BaseTestCase):
    """性能测试基类"""
    
    def setUp(self):
        super().setUp()
        self.performance_baseline = {}
    
    def record_baseline(self, test_name, execution_time):
        """记录性能基线"""
        self.performance_baseline[test_name] = execution_time
    
    def assert_performance_acceptable(self, test_name, execution_time, tolerance=0.1):
        """验证性能是否可接受（不超过基线的10%）"""
        if test_name in self.performance_baseline:
            baseline = self.performance_baseline[test_name]
            max_acceptable = baseline * (1 + tolerance)
            self.assertLessEqual(execution_time, max_acceptable,
                               f"性能下降超过{tolerance*100}%: {execution_time:.4f}s vs 基线{baseline:.4f}s")


if __name__ == '__main__':
    # 运行基础测试
    unittest.main()
