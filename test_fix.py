"""
测试修复后的程序是否能正常显示画面
"""

import sys
import time
import cv2
import numpy as np
from app_state import AppState
import ui_handler
import camera_manager
import config_manager

def test_basic_functionality():
    """测试基本功能是否正常"""
    print("开始测试基本功能...")
    
    # 创建app_state
    app_state = AppState()
    
    # 加载配置
    config_manager.load_config(app_state)
    
    # 初始化摄像头
    if not camera_manager.initialize_camera(app_state):
        print("❌ 摄像头初始化失败")
        return False
    
    print("✅ 摄像头初始化成功")
    
    # 测试核心处理逻辑
    try:
        ui_handler.process_core_logic(app_state)
        print("✅ process_core_logic 执行成功")
    except Exception as e:
        print(f"❌ process_core_logic 执行失败: {e}")
        return False
    
    # 测试显示帧准备
    try:
        ui_handler.prepare_display_frame(app_state)
        if app_state.display_frame is not None:
            print("✅ prepare_display_frame 执行成功，display_frame 不为空")
        else:
            print("⚠️ prepare_display_frame 执行成功，但 display_frame 为空")
    except Exception as e:
        print(f"❌ prepare_display_frame 执行失败: {e}")
        return False
    
    # 清理
    camera_manager.release_camera(app_state)
    print("✅ 基本功能测试完成")
    return True

def test_display_loop():
    """测试显示循环"""
    print("开始测试显示循环...")
    
    # 创建app_state
    app_state = AppState()
    
    # 加载配置
    config_manager.load_config(app_state)
    
    # 初始化摄像头
    if not camera_manager.initialize_camera(app_state):
        print("❌ 摄像头初始化失败")
        return False
    
    # 创建窗口
    cv2.namedWindow("Test Window", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Test Window", 640, 480)
    
    print("✅ 开始显示循环，按 'q' 退出...")
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            # 执行核心处理
            ui_handler.process_core_logic(app_state)
            
            # 准备显示帧
            ui_handler.prepare_display_frame(app_state)
            
            # 显示帧
            if app_state.display_frame is not None:
                cv2.imshow("Test Window", app_state.display_frame)
                frame_count += 1
            else:
                # 显示黑色帧表示没有内容
                black_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(black_frame, "No Frame Available", (50, 240), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                cv2.imshow("Test Window", black_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            
            # 每5秒显示一次统计
            if frame_count % 150 == 0 and frame_count > 0:
                elapsed = time.time() - start_time
                fps = frame_count / elapsed
                print(f"已处理 {frame_count} 帧，平均FPS: {fps:.1f}")
    
    except KeyboardInterrupt:
        print("用户中断")
    except Exception as e:
        print(f"❌ 显示循环出错: {e}")
        return False
    finally:
        # 清理
        cv2.destroyAllWindows()
        camera_manager.release_camera(app_state)
    
    print("✅ 显示循环测试完成")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("UI Handler 修复测试")
    print("=" * 50)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("❌ 基本功能测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 询问是否进行显示测试
    response = input("是否进行显示循环测试？(y/n): ").lower().strip()
    if response == 'y' or response == 'yes':
        if not test_display_loop():
            print("❌ 显示循环测试失败")
            sys.exit(1)
    
    print("\n🎉 所有测试通过！修复成功！")
