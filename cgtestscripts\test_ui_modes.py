"""
UI模式处理模块测试 - 验证ui_modes.py功能
测试从ui_handler.py拆分出的模式处理相关函数
"""

import sys
import os
import unittest
import cv2
import numpy as np
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_base import BaseTestCase
import ui_modes
from app_state import AppState
from constants import *


class TestUIModes(BaseTestCase):
    """UI模式处理功能测试"""
    
    def setUp(self):
        super().setUp()
        # 模拟get_key函数
        self.mock_get_key = Mock(return_value=-1)  # 默认无按键
        ui_modes.get_key = self.mock_get_key
        
        # 模拟其他依赖
        self.setup_mocks()
    
    def setup_mocks(self):
        """设置模拟对象"""
        # 模拟camera_manager
        self.mock_camera_manager = Mock()
        ui_modes.camera_manager = self.mock_camera_manager
        
        # 模拟config_manager
        self.mock_config_manager = Mock()
        ui_modes.config_manager = self.mock_config_manager
        
        # 模拟handle_roi_fine_tune
        self.mock_handle_roi_fine_tune = Mock(return_value=False)
        ui_modes.handle_roi_fine_tune = self.mock_handle_roi_fine_tune
    
    def test_camera_settings_mode_function_exists(self):
        """测试摄像头设置模式函数是否存在且可调用"""
        self.assert_function_callable(ui_modes, '_run_camera_settings_mode')
    
    def test_calibration_mode_function_exists(self):
        """测试校准模式函数是否存在且可调用"""
        self.assert_function_callable(ui_modes, '_run_calibration_mode')
    
    def test_camera_settings_mode_normal_operation(self):
        """测试摄像头设置模式正常操作"""
        # 设置摄像头设置模式
        self.app_state.current_mode = MODE_CAMERA_SETTINGS
        
        # 执行模式处理
        ui_modes._run_camera_settings_mode(self.app_state)
        
        # 验证状态消息被设置
        self.assertIsNotNone(self.app_state.status_message)
        self.assertIsNotNone(self.app_state.prompt_message)
        
        # 验证显示帧被设置
        self.assert_frame_valid(self.app_state.display_frame)
    
    def test_camera_settings_mode_no_camera(self):
        """测试摄像头设置模式无摄像头情况"""
        # 设置无摄像头状态
        self.app_state.cap = None
        
        # 执行模式处理
        ui_modes._run_camera_settings_mode(self.app_state)
        
        # 验证错误处理
        self.assertIn("Error", self.app_state.prompt_message)
        self.assertFalse(self.app_state.running)
    
    def test_camera_settings_mode_key_handling(self):
        """测试摄像头设置模式按键处理"""
        # 测试退出按键
        self.mock_get_key.return_value = ord('q')
        ui_modes._run_camera_settings_mode(self.app_state)
        self.assertFalse(self.app_state.running)
        
        # 重置状态
        self.app_state.running = True
        
        # 测试保存按键
        self.mock_get_key.return_value = ord('s')
        ui_modes._run_camera_settings_mode(self.app_state)
        self.mock_config_manager.save_config.assert_called_with(self.app_state)
        
        # 测试进入校准模式
        self.mock_get_key.return_value = 13  # Enter
        ui_modes._run_camera_settings_mode(self.app_state)
        self.assertEqual(self.app_state.current_mode, MODE_CALIBRATION)
        self.assertEqual(self.app_state.current_calib_state, CALIB_STATE_START)
    
    def test_camera_settings_mode_resolution_change(self):
        """测试分辨率改变处理"""
        original_index = self.app_state.current_resolution_index
        
        # 测试增加分辨率
        self.mock_get_key.return_value = ord('T')
        ui_modes._run_camera_settings_mode(self.app_state)
        
        # 验证分辨率索引改变
        expected_index = (original_index + 1) % len(RESOLUTION_PRESETS)
        self.assertEqual(self.app_state.current_resolution_index, expected_index)
        
        # 验证摄像头设置被重新应用
        self.mock_camera_manager.apply_camera_settings.assert_called()
    
    def test_camera_settings_mode_exposure_adjustment(self):
        """测试曝光调整"""
        original_exposure = self.app_state.exposure_value
        
        # 测试增加曝光
        self.mock_get_key.return_value = ord('E')
        ui_modes._run_camera_settings_mode(self.app_state)
        
        # 验证曝光值改变
        self.assertEqual(self.app_state.exposure_value, original_exposure + EXPOSURE_STEP)
        
        # 验证摄像头属性被设置
        self.app_state.cap.set.assert_called_with(cv2.CAP_PROP_EXPOSURE, float(self.app_state.exposure_value))
    
    def test_camera_settings_mode_brightness_adjustment(self):
        """测试亮度调整"""
        original_brightness = self.app_state.brightness_value
        
        # 测试增加亮度
        self.mock_get_key.return_value = ord('B')
        ui_modes._run_camera_settings_mode(self.app_state)
        
        # 验证亮度值改变
        self.assertEqual(self.app_state.brightness_value, original_brightness + BRIGHTNESS_STEP)
        
        # 验证摄像头属性被设置
        self.app_state.cap.set.assert_called_with(cv2.CAP_PROP_BRIGHTNESS, float(self.app_state.brightness_value))
    
    def test_calibration_mode_normal_operation(self):
        """测试校准模式正常操作"""
        # 设置校准模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_START
        
        # 执行模式处理
        ui_modes._run_calibration_mode(self.app_state)
        
        # 验证显示帧被设置
        self.assert_frame_valid(self.app_state.display_frame)
        
        # 验证微调处理被调用
        self.mock_handle_roi_fine_tune.assert_called_with(self.app_state, -1)
    
    def test_calibration_mode_no_camera(self):
        """测试校准模式无摄像头情况"""
        # 设置无摄像头状态
        self.app_state.cap = None
        
        # 执行模式处理
        ui_modes._run_calibration_mode(self.app_state)
        
        # 验证错误处理
        self.assertIn("Error", self.app_state.prompt_message)
        self.assertFalse(self.app_state.running)
    
    def test_calibration_mode_static_image_usage(self):
        """测试校准模式使用静态图像"""
        # 设置数码管ROI选择状态和静态图像
        self.app_state.current_calib_state = CALIB_STATE_DIGIT_ROI_SELECT_1
        self.app_state.digit_calibration_image_88 = self.create_test_frame()
        
        # 执行模式处理
        ui_modes._run_calibration_mode(self.app_state)
        
        # 验证使用了静态图像
        self.assertTrue(np.array_equal(self.app_state.display_frame, self.app_state.digit_calibration_image_88))
    
    def test_performance_camera_settings_mode(self):
        """测试摄像头设置模式性能"""
        # 测量性能
        result, execution_time = self.measure_performance(ui_modes._run_camera_settings_mode, self.app_state)
        
        # 验证性能（应该在50ms内完成）
        self.assertLess(execution_time, 0.05, 
                       f"_run_camera_settings_mode执行时间过长: {execution_time:.4f}s")
    
    def test_performance_calibration_mode(self):
        """测试校准模式性能"""
        # 设置校准模式
        self.app_state.current_calib_state = CALIB_STATE_START
        
        # 测量性能
        result, execution_time = self.measure_performance(ui_modes._run_calibration_mode, self.app_state)
        
        # 验证性能（应该在50ms内完成）
        self.assertLess(execution_time, 0.05, 
                       f"_run_calibration_mode执行时间过长: {execution_time:.4f}s")


if __name__ == '__main__':
    unittest.main()
