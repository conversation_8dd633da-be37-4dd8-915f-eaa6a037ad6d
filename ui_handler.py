"""
UI处理器主模块 - 重构后的版本
协调各个UI子模块，保持原有接口不变

重构说明：
- 导入并使用拆分出的ui_drawing、ui_modes、ui_events模块
- 保持所有公共接口和函数签名不变
- 仅作为协调器，不包含具体实现逻辑
"""

import cv2
import numpy as np
import time
import functools
import logging
import subprocess
import os
import sys
import re
import traceback

# Windows API for window topmost functionality
try:
    import win32gui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from app_state import AppState
from roi_fine_tune import handle_roi_fine_tune, draw_fine_tune_info, init_fine_tune_state
from constants import *
import camera_manager
import config_manager
import led_detector
import digit_detector
from cpu_communicator import send_value_to_cpu
import base_point_manager
import async_task_manager
from async_task_manager import TaskType, TaskStatus
from analyze_led_log import analyze_led_cycles

# 导入拆分出的模块
import ui_drawing
import ui_modes
import ui_events


# 导出原有的公共接口函数，保持向后兼容
def set_shared_state(shared_state):
    """设置共享状态，用于线程模式下的按键处理"""
    return ui_events.set_shared_state(shared_state)


def get_key():
    """获取按键，兼容单线程和多线程模式"""
    return ui_events.get_key()


def toggle_window_topmost(window_title, topmost):
    """切换窗口置顶状态，优先使用 Windows API，回退到 OpenCV"""
    return ui_events.toggle_window_topmost(window_title, topmost)


def setup_mouse_callback(window_name: str, app_state: AppState):
    """设置鼠标回调函数"""
    return ui_events.setup_mouse_callback(window_name, app_state)


def draw_rois(app_state: AppState):
    """在 display_frame 上绘制所有相关的 ROI"""
    return ui_drawing.draw_rois(app_state)


def draw_hud(app_state: AppState):
    """在 display_frame 上绘制状态信息和提示（带 HUD 缓存与降频刷新）"""
    return ui_drawing.draw_hud(app_state)


def _run_camera_settings_mode(app_state: AppState):
    """处理摄像头参数设置模式的逻辑和按键"""
    return ui_modes._run_camera_settings_mode(app_state)


def _run_calibration_mode(app_state: AppState):
    """处理校准模式的逻辑和按键"""
    return ui_modes._run_calibration_mode(app_state)


def _run_detection_mode(app_state: AppState):
    """处理检测模式的逻辑和按键，包含 "88" 触发的分析流程"""
    return ui_modes._run_detection_mode(app_state)


def process_core_logic(app_state: AppState):
    """核心处理逻辑 - 根据当前模式调用相应的处理函数"""
    try:
        if app_state.current_mode == MODE_CAMERA_SETTINGS:
            _run_camera_settings_mode(app_state)
        elif app_state.current_mode == MODE_CALIBRATION:
            _run_calibration_mode(app_state)
        elif app_state.current_mode == MODE_DETECTION:
            _run_detection_mode(app_state)
        else:
            app_state.prompt_message = f"Unknown mode: {app_state.current_mode}"
            app_state.running = False
    except Exception as e:
        logging.error(f"Error in process_core_logic: {e}")
        logging.error(traceback.format_exc())
        app_state.prompt_message = f"Error: {str(e)}"
        app_state.running = False


def prepare_display_frame(app_state: AppState):
    """准备显示帧 - 绘制ROI和HUD信息"""
    try:
        if app_state.display_frame is not None:
            # 绘制ROI
            draw_rois(app_state)
            
            # 绘制微调信息（如果处于微调状态）
            draw_fine_tune_info(app_state, app_state.display_frame)
            
            # 绘制HUD
            draw_hud(app_state)
    except Exception as e:
        logging.error(f"Error in prepare_display_frame: {e}")
        logging.error(traceback.format_exc())


# 保持原有的内部函数引用，确保完全兼容
_mouse_callback = ui_events._mouse_callback


# 为了完全兼容，确保ui_modes模块能够访问get_key函数
ui_modes.get_key = get_key
