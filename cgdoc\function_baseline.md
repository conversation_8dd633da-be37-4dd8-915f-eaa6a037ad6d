# UI Handler 功能基线记录

## 📊 原始文件信息
- **文件名**: ui_handler.py
- **总行数**: 1956行
- **文件大小**: 约80KB
- **创建时间**: 2025-08-28

## 🔍 函数列表分析

### 全局函数 (行1-110)
- `set_shared_state()` - 设置共享状态
- `get_key()` - 获取按键输入
- `toggle_window_topmost()` - 窗口置顶功能

### 鼠标事件处理 (行111-284)
- `_mouse_callback()` - 鼠标回调函数 (主要函数，167行)
- `setup_mouse_callback()` - 设置鼠标回调

### 绘制系统 (行287-714)
- `draw_rois()` - ROI绘制函数 (266行)
- `draw_hud()` - HUD信息显示函数 (159行)

### 模式处理函数 (行716-1956)
- `_run_camera_settings_mode()` - 摄像头设置模式 (82行)
- `_run_calibration_mode()` - 校准模式处理 (656行，最大函数)
- `_run_detection_mode()` - 检测模式处理 (约500行)
- `process_core_logic()` - 核心处理逻辑
- `prepare_display_frame()` - 显示帧准备

## 📦 模块依赖关系

### 导入的模块
```python
import cv2
import numpy as np
import time
import functools
import logging
import subprocess
import os
import sys
import re
import traceback
```

### Windows API依赖
```python
try:
    import win32gui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False
```

### 项目内部依赖
```python
from app_state import AppState
from roi_fine_tune import handle_roi_fine_tune, draw_fine_tune_info, init_fine_tune_state
from constants import *
import camera_manager
import config_manager
import led_detector
import digit_detector
from cpu_communicator import send_value_to_cpu
import base_point_manager
import async_task_manager
from async_task_manager import TaskType, TaskStatus
from analyze_led_log import analyze_led_cycles
```

## 🎯 关键功能点

### 状态机管理
- 3个主要模式：摄像头设置、校准、检测
- 多个校准子状态：基准点选择、LED ROI选择、数码管校准等

### 用户交互
- 鼠标拖拽选择ROI
- 键盘快捷键操作
- 模板复制功能
- ROI编辑和微调

### 绘制功能
- LED ROI状态显示
- 数码管段状态显示
- 实时HUD信息
- 基准点标记

### 校准流程
- 基准点校准
- LED样本采集
- 数码管段选择
- 阈值调整

## 🔧 核心算法调用

### LED检测
- `led_detector.led_capture_samples()`
- `led_detector.led_calculate_thresholds()`
- `led_detector.detect_led_status()`

### 数码管检测
- `digit_detector.detect_digit_status()`
- `digit_detector.capture_digit_calibration_image()`

### 基准点管理
- `base_point_manager.extract_base_template()`
- `base_point_manager.update_roi_positions()`

## 📈 性能特征

### 关键性能指标
- FPS处理能力：约30-60 FPS
- HUD刷新频率：10Hz (缓存机制)
- 鼠标响应延迟：<10ms
- 状态切换时间：<100ms

### 内存使用
- HUD缓存：约2-4MB (取决于分辨率)
- 图像帧缓存：约1-8MB (取决于分辨率)
- 状态数据：约1MB

## ⚠️ 关键约束

### 线程安全
- 主线程GUI显示
- 处理线程后台运行
- 共享状态访问控制

### 错误处理
- 摄像头异常处理
- ROI边界检查
- 状态转换验证

### 兼容性要求
- Windows API回退机制
- OpenCV版本兼容
- 多分辨率支持

## 🎨 UI特性

### 视觉反馈
- 实时ROI预览
- 状态颜色编码
- 进度提示信息

### 交互体验
- 拖拽选择ROI
- 快捷键操作
- 上下文提示

## 📝 重构注意事项

### 必须保持的特性
1. 所有函数签名不变
2. 状态机逻辑完全一致
3. 用户交互体验相同
4. 性能水平不下降
5. 错误处理机制完整

### 风险控制点
1. 模块间依赖关系
2. 全局变量访问
3. 线程安全性
4. 内存管理
5. 异常处理链
