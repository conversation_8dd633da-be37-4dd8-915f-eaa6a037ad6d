"""
最终验证测试 - 验证重构后的系统完整性
确保重构后的ui_handler.py能够正常工作
"""

import sys
import os
import unittest
import cv2
import numpy as np
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_base import BaseTestCase
from app_state import AppState
from constants import *


class TestFinalVerification(BaseTestCase):
    """最终验证测试类"""
    
    def setUp(self):
        super().setUp()
        # 设置完整的测试环境
        self.setup_complete_environment()
    
    def setup_complete_environment(self):
        """设置完整的测试环境"""
        # LED配置
        self.app_state.led_rois = [
            (100, 100, 50, 50),  # G1
            (200, 100, 50, 50),  # G2
            (300, 100, 50, 50),  # R1
            (400, 100, 50, 50),  # R2
        ]
        self.app_state.led_num_green = 2
        self.app_state.led_num_red = 2
        self.app_state.led_max_rois = 4
        self.app_state.led_last_status = [True, False, True, False]
        self.app_state.led_last_values = [(200, 180, 100), (100, 90, 50), (150, 80, 200), (80, 70, 60)]
        
        # 数码管配置
        self.app_state.digit_rois = [
            (100, 300, 80, 120),  # D1
            (200, 300, 80, 120),  # D2
        ]
        self.app_state.digit_segment_rois = [
            [(110, 310, 60, 10), (170, 320, 10, 40), (170, 370, 10, 40), 
             (110, 400, 60, 10), (100, 370, 10, 40), (100, 320, 10, 40), (110, 355, 60, 10)],
            [(210, 310, 60, 10), (270, 320, 10, 40), (270, 370, 10, 40), 
             (210, 400, 60, 10), (200, 370, 10, 40), (200, 320, 10, 40), (210, 355, 60, 10)],
        ]
        self.app_state.digit_last_recognized_chars = ['8', '5']
        self.app_state.digit_last_segment_patterns = [
            [1, 1, 1, 1, 1, 1, 1],
            [1, 0, 1, 1, 0, 1, 1],
        ]
        self.app_state.digit_last_missing_segments = [[], []]
        
        # 其他状态
        self.app_state.fps = 30.5
        self.app_state.window_topmost = True
        self.app_state.digit_brightness_threshold = 50.0
    
    def test_import_ui_handler(self):
        """测试能够成功导入ui_handler模块"""
        try:
            import ui_handler
            self.assertTrue(True, "成功导入ui_handler模块")
        except ImportError as e:
            self.fail(f"无法导入ui_handler模块: {e}")
    
    def test_all_public_interfaces_exist(self):
        """测试所有公共接口都存在"""
        import ui_handler
        
        required_functions = [
            'set_shared_state',
            'get_key',
            'toggle_window_topmost',
            'setup_mouse_callback',
            'draw_rois',
            'draw_hud',
            'process_core_logic',
            'prepare_display_frame',
        ]
        
        for func_name in required_functions:
            self.assertTrue(hasattr(ui_handler, func_name),
                           f"ui_handler缺少函数: {func_name}")
            self.assertTrue(callable(getattr(ui_handler, func_name)),
                           f"函数{func_name}不可调用")
    
    def test_camera_settings_mode(self):
        """测试摄像头设置模式"""
        import ui_handler
        
        # 设置摄像头设置模式
        self.app_state.current_mode = MODE_CAMERA_SETTINGS
        
        # 模拟依赖
        with patch('ui_modes.camera_manager') as mock_camera_manager, \
             patch('ui_modes.config_manager') as mock_config_manager:
            
            # 执行处理
            ui_handler.process_core_logic(self.app_state)
            
            # 验证状态消息被设置
            self.assertIsNotNone(self.app_state.status_message)
            self.assertIsNotNone(self.app_state.prompt_message)
    
    def test_calibration_mode(self):
        """测试校准模式"""
        import ui_handler
        
        # 设置校准模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_START
        
        # 模拟依赖
        with patch('ui_modes.config_manager') as mock_config_manager, \
             patch('ui_modes.handle_roi_fine_tune', return_value=False):
            
            # 执行处理
            ui_handler.process_core_logic(self.app_state)
            
            # 验证提示消息被设置
            self.assertIsNotNone(self.app_state.prompt_message)
    
    def test_detection_mode(self):
        """测试检测模式"""
        import ui_handler
        
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 模拟依赖
        with patch('ui_modes.led_detector') as mock_led_detector, \
             patch('ui_modes.digit_detector') as mock_digit_detector, \
             patch('ui_modes.base_point_manager') as mock_base_point_manager:
            
            # 模拟digit_detector返回值
            mock_digit_detector.detect_digit_status.return_value = (['8', '5'], [[1,1,1,1,1,1,1], [1,0,1,1,0,1,1]])
            
            # 执行处理
            ui_handler.process_core_logic(self.app_state)
            
            # 验证检测函数被调用
            mock_led_detector.detect_led_status.assert_called_once()
            mock_digit_detector.detect_digit_status.assert_called_once()
    
    def test_draw_functions(self):
        """测试绘制功能"""
        import ui_handler
        
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 测试draw_rois
        original_frame = self.app_state.display_frame.copy()
        ui_handler.draw_rois(self.app_state)
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame),
                        "draw_rois应该修改显示帧")
        
        # 测试draw_hud
        ui_handler.draw_hud(self.app_state)
        self.assertIsNotNone(self.app_state.display_frame)
    
    def test_prepare_display_frame(self):
        """测试显示帧准备"""
        import ui_handler
        
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 执行显示帧准备
        original_frame = self.app_state.display_frame.copy()
        ui_handler.prepare_display_frame(self.app_state)
        
        # 验证显示帧被处理
        self.assertIsNotNone(self.app_state.display_frame)
        # 由于绘制了内容，帧应该有变化
        self.assertFalse(np.array_equal(original_frame, self.app_state.display_frame))
    
    def test_event_handling(self):
        """测试事件处理"""
        import ui_handler
        
        # 测试get_key
        with patch('cv2.waitKey', return_value=ord('a')):
            key = ui_handler.get_key()
            self.assertEqual(key, ord('a'))
        
        # 测试设置鼠标回调
        with patch('cv2.setMouseCallback') as mock_set_callback:
            ui_handler.setup_mouse_callback("test_window", self.app_state)
            mock_set_callback.assert_called_once()
        
        # 测试窗口置顶
        with patch('cv2.setWindowProperty') as mock_set_property:
            result = ui_handler.toggle_window_topmost("test_window", True)
            # 应该调用了OpenCV函数或Windows API
            self.assertIsInstance(result, bool)
    
    def test_performance(self):
        """测试性能"""
        import ui_handler
        
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        
        # 模拟依赖以避免实际的检测计算
        with patch('ui_modes.led_detector'), \
             patch('ui_modes.digit_detector') as mock_digit_detector, \
             patch('ui_modes.base_point_manager'):
            
            mock_digit_detector.detect_digit_status.return_value = (['8', '5'], [[1,1,1,1,1,1,1], [1,0,1,1,0,1,1]])
            
            # 测试process_core_logic性能
            result, execution_time = self.measure_performance(
                ui_handler.process_core_logic, self.app_state
            )
            self.assertLess(execution_time, 0.1, 
                           f"process_core_logic执行时间过长: {execution_time:.4f}s")
            
            # 测试prepare_display_frame性能
            result, execution_time = self.measure_performance(
                ui_handler.prepare_display_frame, self.app_state
            )
            self.assertLess(execution_time, 0.05, 
                           f"prepare_display_frame执行时间过长: {execution_time:.4f}s")
    
    def test_error_handling(self):
        """测试错误处理"""
        import ui_handler
        
        # 测试无效模式
        self.app_state.current_mode = 999
        ui_handler.process_core_logic(self.app_state)
        
        # 验证错误被正确处理
        self.assertFalse(self.app_state.running)
        self.assertIn("Unknown mode", self.app_state.prompt_message)
    
    def test_module_structure(self):
        """测试模块结构"""
        # 验证所有拆分的模块都能正常导入
        try:
            import ui_drawing
            import ui_modes
            import ui_events
            self.assertTrue(True, "所有子模块都能正常导入")
        except ImportError as e:
            self.fail(f"子模块导入失败: {e}")
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        import ui_handler
        
        # 验证原有的内部函数引用仍然存在
        self.assertTrue(hasattr(ui_handler, '_mouse_callback'),
                       "缺少_mouse_callback内部函数引用")
        
        # 验证所有原有接口都存在且可调用
        original_interfaces = [
            'set_shared_state', 'get_key', 'toggle_window_topmost',
            'setup_mouse_callback', 'draw_rois', 'draw_hud',
            'process_core_logic', 'prepare_display_frame'
        ]
        
        for interface in original_interfaces:
            self.assertTrue(hasattr(ui_handler, interface),
                           f"缺少向后兼容接口: {interface}")
            self.assertTrue(callable(getattr(ui_handler, interface)),
                           f"接口{interface}不可调用")


if __name__ == '__main__':
    unittest.main()
