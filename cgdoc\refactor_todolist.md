# UI Handler 重构 TODO 列表

## 🚀 阶段1：准备工作

### ✅ 已完成
- [x] 创建cgtestscripts目录
- [x] 创建cgdoc目录
- [x] 编写重构计划文档
- [x] 创建TODO列表
- [x] **备份原始文件**
  - [x] 复制ui_handler.py为ui_handler_original.py
  - [x] 验证备份文件完整性
- [x] **功能基线记录**
  - [x] 记录当前所有函数列表
  - [x] 记录模块间依赖关系
  - [x] 记录关键功能点
- [x] **测试框架搭建**
  - [x] 创建基础测试模板
  - [x] 设置测试运行环境
  - [x] 编写功能验证脚本

### 🔄 进行中 - ui_drawing.py 拆分 ✅
- [x] **函数识别**
  - [x] `draw_rois()` (行287-553) - 已提取
  - [x] `draw_hud()` (行555-714) - 已提取
  - [x] 相关辅助函数 - 无需额外提取

- [x] **拆分执行**
  - [x] 创建ui_drawing.py文件
  - [x] 移动绘制相关函数
  - [x] 保持函数签名不变
  - [x] 添加必要的导入语句

- [x] **测试验证**
  - [x] 编写ui_drawing单元测试
  - [x] 验证绘制功能正常 - 11个测试全部通过
  - [x] 性能对比测试 - 通过

## 🔧 阶段2：模块拆分

### ui_drawing.py 拆分
- [ ] **函数识别**
  - [ ] `draw_rois()` (行287-553)
  - [ ] `draw_hud()` (行555-714)
  - [ ] 相关辅助函数

- [ ] **拆分执行**
  - [ ] 创建ui_drawing.py文件
  - [ ] 移动绘制相关函数
  - [ ] 保持函数签名不变
  - [ ] 添加必要的导入语句

- [ ] **测试验证**
  - [ ] 编写ui_drawing单元测试
  - [ ] 验证绘制功能正常
  - [ ] 性能对比测试

### ui_modes.py 拆分  
- [ ] **函数识别**
  - [ ] `_run_camera_settings_mode()` (行718-798)
  - [ ] `_run_calibration_mode()` (行800-1400+)
  - [ ] `_run_detection_mode()` (行1400+)

- [ ] **拆分执行**
  - [ ] 创建ui_modes.py文件
  - [ ] 移动模式处理函数
  - [ ] 保持状态机逻辑不变
  - [ ] 添加必要的导入语句

- [ ] **测试验证**
  - [ ] 编写ui_modes单元测试
  - [ ] 验证状态转换正常
  - [ ] 模式功能完整性测试

### ui_events.py 拆分
- [ ] **函数识别**
  - [ ] `_mouse_callback()` (行111-278)
  - [ ] `setup_mouse_callback()` (行280-284)
  - [ ] `get_key()` (行29-36)
  - [ ] `toggle_window_topmost()` (行38-86)

- [ ] **拆分执行**
  - [ ] 创建ui_events.py文件
  - [ ] 移动事件处理函数
  - [ ] 保持事件响应逻辑不变
  - [ ] 添加必要的导入语句

- [ ] **测试验证**
  - [ ] 编写ui_events单元测试
  - [ ] 验证鼠标键盘响应
  - [ ] 事件处理完整性测试

## 🔗 阶段3：集成验证

### 主文件重构
- [ ] **ui_handler.py更新**
  - [ ] 添加新模块导入
  - [ ] 保留核心协调函数
  - [ ] 确保接口兼容性

- [ ] **集成测试**
  - [ ] 运行完整功能测试
  - [ ] 验证模块间协作
  - [ ] 性能基准对比

### 系统验证
- [ ] **功能验证**
  - [ ] 摄像头设置功能
  - [ ] 校准流程完整性
  - [ ] 检测模式正常运行
  - [ ] UI交互响应正常

- [ ] **性能验证**
  - [ ] FPS性能对比
  - [ ] 内存使用对比
  - [ ] 响应时间测试

## 📚 阶段4：文档和清理

### 文档完善
- [ ] **更新文档**
  - [ ] 记录重构结果
  - [ ] 更新模块说明
  - [ ] 编写使用指南

- [ ] **测试报告**
  - [ ] 汇总测试结果
  - [ ] 性能对比报告
  - [ ] 问题和解决方案

### 清理工作
- [ ] **代码清理**
  - [ ] 移除临时文件
  - [ ] 优化导入语句
  - [ ] 代码格式统一

- [ ] **回滚指南**
  - [ ] 编写详细回滚步骤
  - [ ] 验证回滚流程
  - [ ] 应急处理方案

## ⚠️ 风险控制

### 关键检查点
- [ ] 每个模块拆分后立即测试
- [ ] 保持原始功能100%不变
- [ ] 核心算法模块绝不修改
- [ ] 任何问题立即回滚

### 验证标准
- [ ] 所有现有功能正常
- [ ] 性能无明显下降
- [ ] 用户体验保持一致
- [ ] 错误处理机制完整

## 📝 注意事项

1. **编码规范**：遵循项目现有编码风格
2. **避免冗余**：不增加不必要的抽象层
3. **保持简洁**：只做必要的拆分
4. **谨慎执行**：每步都要验证和测试
