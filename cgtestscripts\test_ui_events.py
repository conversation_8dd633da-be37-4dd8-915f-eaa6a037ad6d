"""
UI事件处理模块测试 - 验证ui_events.py功能
测试从ui_handler.py拆分出的事件处理相关函数
"""

import sys
import os
import unittest
import cv2
import numpy as np
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from test_base import BaseTestCase
import ui_events
from app_state import AppState
from constants import *


class TestUIEvents(BaseTestCase):
    """UI事件处理功能测试"""
    
    def setUp(self):
        super().setUp()
        # 设置测试用的ROI数据
        self.setup_test_rois()
        
        # 模拟base_point_manager
        self.mock_base_point_manager = Mock()
        ui_events.base_point_manager = self.mock_base_point_manager
    
    def setup_test_rois(self):
        """设置测试用的ROI数据"""
        # LED ROI测试数据
        self.app_state.led_rois = [
            (100, 100, 50, 50),  # G1
            (200, 100, 50, 50),  # G2
            (300, 100, 50, 50),  # R1
            None,  # 未定义的ROI
        ]
        self.app_state.led_num_green = 2
        self.app_state.led_num_red = 2
        self.app_state.led_max_rois = 4
        
        # 基准点测试数据
        self.app_state.base_points = [None, None]
        self.app_state.base_templates = [None, None]
        self.app_state.calib_base_point_index = 0
        self.app_state.base_template_size = 30
    
    def test_get_key_function_exists(self):
        """测试get_key函数是否存在且可调用"""
        self.assert_function_callable(ui_events, 'get_key')
    
    def test_toggle_window_topmost_function_exists(self):
        """测试toggle_window_topmost函数是否存在且可调用"""
        self.assert_function_callable(ui_events, 'toggle_window_topmost')
    
    def test_mouse_callback_function_exists(self):
        """测试_mouse_callback函数是否存在且可调用"""
        self.assert_function_callable(ui_events, '_mouse_callback')
    
    def test_setup_mouse_callback_function_exists(self):
        """测试setup_mouse_callback函数是否存在且可调用"""
        self.assert_function_callable(ui_events, 'setup_mouse_callback')
    
    def test_get_key_single_thread_mode(self):
        """测试单线程模式下的按键获取"""
        # 确保没有设置共享状态
        ui_events.set_shared_state(None)
        
        # 模拟cv2.waitKey
        with patch('cv2.waitKey', return_value=ord('a')):
            key = ui_events.get_key()
            self.assertEqual(key, ord('a'))
    
    def test_get_key_multi_thread_mode(self):
        """测试多线程模式下的按键获取"""
        # 创建模拟的共享状态
        mock_shared_state = Mock()
        mock_shared_state.get_and_clear_key.return_value = ord('b')
        
        # 设置共享状态
        ui_events.set_shared_state(mock_shared_state)
        
        # 测试按键获取
        key = ui_events.get_key()
        self.assertEqual(key, ord('b'))
        mock_shared_state.get_and_clear_key.assert_called_once()
        
        # 清理
        ui_events.set_shared_state(None)
    
    def test_set_shared_state(self):
        """测试设置共享状态"""
        mock_state = Mock()
        ui_events.set_shared_state(mock_state)
        
        # 验证全局变量被设置
        self.assertEqual(ui_events._shared_state, mock_state)
        
        # 清理
        ui_events.set_shared_state(None)
    
    @patch('ui_events.WIN32_AVAILABLE', True)
    @patch('ui_events.win32gui')
    def test_toggle_window_topmost_windows_api(self, mock_win32gui):
        """测试Windows API方式的窗口置顶"""
        # 模拟找到窗口
        mock_win32gui.FindWindow.return_value = 12345
        
        # 测试置顶
        result = ui_events.toggle_window_topmost("Test Window", True)
        
        # 验证调用
        mock_win32gui.FindWindow.assert_called_with(None, "Test Window")
        mock_win32gui.SetWindowPos.assert_called_once()
        self.assertTrue(result)
    
    @patch('ui_events.WIN32_AVAILABLE', False)
    @patch('cv2.setWindowProperty')
    def test_toggle_window_topmost_opencv_fallback(self, mock_set_window_property):
        """测试OpenCV回退方式的窗口置顶"""
        # 测试置顶
        result = ui_events.toggle_window_topmost("Test Window", True)
        
        # 验证OpenCV调用
        mock_set_window_property.assert_called_with("Test Window", cv2.WND_PROP_TOPMOST, 1)
        self.assertTrue(result)
    
    def test_mouse_callback_not_in_calibration_mode(self):
        """测试非校准模式下的鼠标回调"""
        # 设置检测模式
        self.app_state.current_mode = MODE_DETECTION
        self.app_state.selecting_roi = True
        self.app_state.current_rect = (10, 10, 20, 20)
        
        # 调用鼠标回调
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 100, 100, 0, None, self.app_state)
        
        # 验证状态被重置
        self.assertFalse(self.app_state.selecting_roi)
        self.assertIsNone(self.app_state.current_rect)
    
    def test_mouse_callback_base_point_selection(self):
        """测试基准点选择模式下的鼠标回调"""
        # 设置基准点选择模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
        self.app_state.calib_base_point_index = 0
        
        # 模拟成功提取基准点模板
        mock_template = np.ones((30, 30, 3), dtype=np.uint8)
        self.mock_base_point_manager.extract_base_template.return_value = mock_template
        
        # 模拟鼠标点击
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 100, 100, 0, None, self.app_state)
        
        # 验证基准点被设置
        self.assertEqual(self.app_state.base_points[0], (100, 100))
        self.assertEqual(self.app_state.calib_base_point_index, 1)
        self.mock_base_point_manager.extract_base_template.assert_called_once()
    
    def test_mouse_callback_led_roi_selection(self):
        """测试LED ROI选择模式下的鼠标回调"""
        # 设置LED ROI选择模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        self.app_state.calib_led_roi_index = 0
        
        # 模拟鼠标拖拽
        # 按下
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 50, 50, 0, None, self.app_state)
        self.assertTrue(self.app_state.selecting_roi)
        self.assertEqual(self.app_state.roi_start_point, (50, 50))
        
        # 移动
        ui_events._mouse_callback(cv2.EVENT_MOUSEMOVE, 100, 100, 0, None, self.app_state)
        self.assertEqual(self.app_state.current_rect, (50, 50, 50, 50))
        
        # 释放
        ui_events._mouse_callback(cv2.EVENT_LBUTTONUP, 100, 100, 0, None, self.app_state)
        self.assertFalse(self.app_state.selecting_roi)
        self.assertEqual(self.app_state.current_rect, (50, 50, 50, 50))
    
    def test_mouse_callback_led_edit_mode(self):
        """测试LED编辑模式下的鼠标回调"""
        # 设置LED编辑模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_EDIT
        
        # 模拟点击ROI
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 125, 125, 0, None, self.app_state)
        
        # 验证ROI被选中
        self.assertEqual(self.app_state.selected_roi_index, 0)  # 点击在第一个ROI内
        self.assertTrue(self.app_state.moving_roi)
        self.assertEqual(self.app_state.move_start_pos, (125, 125))
    
    def test_mouse_callback_template_mode(self):
        """测试模板模式下的鼠标回调"""
        # 设置模板模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        self.app_state.template_mode = True
        self.app_state.template_roi = (40, 30)  # w, h
        
        # 模拟鼠标移动
        ui_events._mouse_callback(cv2.EVENT_MOUSEMOVE, 100, 100, 0, None, self.app_state)
        
        # 验证模板预览位置
        expected_preview = (100 - 20, 100 - 15, 40, 30)  # 以鼠标为中心
        self.assertEqual(self.app_state.template_preview_pos, expected_preview)
        
        # 模拟鼠标点击
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 100, 100, 0, None, self.app_state)
        
        # 验证ROI被设置
        self.assertEqual(self.app_state.current_rect, expected_preview)
    
    def test_mouse_callback_invalid_roi_size(self):
        """测试无效ROI尺寸的处理"""
        # 设置LED ROI选择模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        
        # 模拟创建过小的ROI
        ui_events._mouse_callback(cv2.EVENT_LBUTTONDOWN, 50, 50, 0, None, self.app_state)
        ui_events._mouse_callback(cv2.EVENT_MOUSEMOVE, 52, 52, 0, None, self.app_state)  # 2x2像素
        ui_events._mouse_callback(cv2.EVENT_LBUTTONUP, 52, 52, 0, None, self.app_state)
        
        # 验证无效ROI被重置
        self.assertIsNone(self.app_state.current_rect)
    
    @patch('cv2.setMouseCallback')
    def test_setup_mouse_callback(self, mock_set_mouse_callback):
        """测试设置鼠标回调"""
        window_name = "Test Window"
        
        # 调用设置函数
        ui_events.setup_mouse_callback(window_name, self.app_state)
        
        # 验证OpenCV函数被调用
        mock_set_mouse_callback.assert_called_once()
        args = mock_set_mouse_callback.call_args[0]
        self.assertEqual(args[0], window_name)
        # 第二个参数应该是一个函数
        self.assertTrue(callable(args[1]))
    
    def test_performance_mouse_callback(self):
        """测试鼠标回调性能"""
        # 设置校准模式
        self.app_state.current_mode = MODE_CALIBRATION
        self.app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        
        # 测量性能
        result, execution_time = self.measure_performance(
            ui_events._mouse_callback, 
            cv2.EVENT_LBUTTONDOWN, 100, 100, 0, None, self.app_state
        )
        
        # 验证性能（应该在5ms内完成）
        self.assertLess(execution_time, 0.005, 
                       f"_mouse_callback执行时间过长: {execution_time:.4f}s")


if __name__ == '__main__':
    unittest.main()
